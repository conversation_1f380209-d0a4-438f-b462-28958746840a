# Android APK安装失败修复方案

## 问题描述

Android版本下载成功后安装失败，出现错误：
```
exception failed to start APK installation: platformException file xxx.apk exposed beyond app through intent.getdata(),null,null
```

## 问题原因

这个错误是由于Android 7.0 (API 24) 以后的安全限制导致的：
- 直接使用 `file://` URI 来共享文件会抛出 `FileUriExposedException`
- 需要使用 FileProvider 来安全地共享文件
- 原代码使用了不安全的文件共享方式

## 解决方案

### 1. 添加FileProvider配置

**文件**: `android/app/src/main/AndroidManifest.xml`

添加了FileProvider声明：
```xml
<!-- FileProvider for APK installation -->
<provider
    android:name="androidx.core.content.FileProvider"
    android:authorities="com.panabit.client.fileprovider"
    android:exported="false"
    android:grantUriPermissions="true">
    <meta-data
        android:name="android.support.FILE_PROVIDER_PATHS"
        android:resource="@xml/file_provider_paths" />
</provider>
```

### 2. 创建FileProvider路径配置

**文件**: `android/app/src/main/res/xml/file_provider_paths.xml`

配置了安全的文件共享路径：
```xml
<?xml version="1.0" encoding="utf-8"?>
<paths xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 应用缓存目录 - 主要用于APK文件共享 -->
    <cache-path name="cache" path="." />
    
    <!-- 更新文件专用缓存目录 -->
    <cache-path name="updates" path="updates/" />
    
    <!-- 应用内部文件目录 - 备用 -->
    <files-path name="files" path="." />
    
    <!-- 应用外部缓存目录 - 备用 -->
    <external-cache-path name="external_cache" path="." />
</paths>
```

### 3. 添加原生APK安装方法

**文件**: `MainActivity.kt`

在系统Channel (`panabit_client/system`) 中添加了 `handleInstallApk` 方法：
- 使用FileProvider获取安全的content URI
- 创建正确的安装Intent
- 处理权限和错误情况

**架构设计**：
- 系统级操作（APK安装、应用最小化）放在 `system` channel
- VPN业务逻辑放在 `methods` channel (PlatformChannelHandler)
- 确保APK安装在应用启动时就可用，无需等待用户登录

### 4. 修改Flutter端安装逻辑

**文件**: `android_update_service.dart`

- 移除了不安全的AndroidIntent直接调用
- 改为调用系统Channel的 `installApk` 方法
- 使用 `panabit_client/system` MethodChannel进行通信

### 5. 简化存储管理

采用了用户建议，使用缓存目录：
- 使用 `getTemporaryDirectory()` 获取缓存目录
- 系统自动管理缓存空间，无需手动清理
- 简化了 `cleanupOldFiles` 方法
- 移除了复杂的存储回退逻辑

## 技术优势

### 1. 安全性
- 使用FileProvider符合Android安全最佳实践
- 避免了FileUriExposedException异常
- 正确处理文件权限

### 2. 简化性
- 使用缓存目录，系统自动管理
- 减少了复杂的存储路径逻辑
- 无需手动清理文件

### 3. 兼容性
- 支持Android 7.0+的安全要求
- 保持向后兼容性
- 遵循Android开发规范

## 测试验证

更新了测试文件 `test/android_update_test.dart`：
- 修正了下载目录期望值
- 确保所有测试通过

### 6. 用户体验优化

**简化处理**: 保持原有的简单逻辑
- 启动安装Intent后直接返回成功
- 显示"安装完成"提示，提醒用户重启应用
- 用户在系统对话框中完成实际的安装授权

## 使用说明

修复后的APK安装流程：
1. 下载APK到缓存目录
2. 调用系统Channel的 `installApk` 方法
3. 使用FileProvider生成安全URI
4. 启动系统安装界面
5. 显示用户友好的提示信息
6. 用户手动完成安装和重启

## 注意事项

- 确保应用有安装未知来源应用的权限
- 缓存目录文件由系统自动管理
- 用户需要手动完成安装过程
- 安装完成后需要手动重启应用
