/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// FILE NAME :      update_manager.dart
///
/// DESCRIPTION :    统一的更新管理器，处理所有界面的更新逻辑
///
/// AUTHOR :         wei
///
/// HISTORY :        01/04/2025 create

import 'dart:io';
import 'package:flutter/material.dart';
import '../services/update_service.dart';
import '../models/update_config.dart';
import '../models/update_info.dart';
import '../core/dependency_injection.dart';
import '../services/log_service.dart';

import '../widgets/update_progress_dialog.dart';
import '../generated/l10n/app_localizations.dart';
import 'package:package_info_plus/package_info_plus.dart';

/// UpdateManager
///
/// PURPOSE:
///     统一的更新管理器，提供一致的更新检查和处理逻辑
///
/// FEATURES:
///     - 统一的更新检查接口
///     - 一致的用户交互流程
///     - 统一的进度显示
///     - 错误处理和重试机制
class UpdateManager {
  static final LogService _logService = serviceLocator<LogService>();

  // 全局更新检查时间管理
  static DateTime? _lastCheckTime;
  static const Duration _checkCooldown = Duration(minutes: 60);

  /// 检查是否需要进行更新检查（60分钟冷却）
  static bool _shouldCheckForUpdate() {
    if (_lastCheckTime == null) {
      return true;
    }

    final now = DateTime.now();
    final timeSinceLastCheck = now.difference(_lastCheckTime!);
    return timeSinceLastCheck >= _checkCooldown;
  }

  /// 记录检查时间
  static void _recordCheckTime() {
    _lastCheckTime = DateTime.now();
  }

  /// 检查更新（用于Login和Connect前的检查）
  ///
  /// DESCRIPTION:
  ///     在Login或Connect前检查更新，如果有更新则显示对话框
  ///     支持Windows和Android平台，iOS平台跳过检查
  ///
  /// PARAMETERS:
  ///     context - 构建上下文
  ///     domain - 用户域名（可选）
  ///     timeout - 超时时间（默认2秒）
  ///
  /// RETURNS:
  ///     Future<bool> - true表示继续原操作，false表示用户选择更新
  static Future<bool> checkForUpdateBeforeAction(
    BuildContext context, {
    String? domain,
    Duration timeout = const Duration(seconds: 2),
  }) async {
    try {
      _logService.info('UpdateManager', 'Starting pre-action update check...');
      _logService.info('UpdateManager', 'Platform: ${Platform.operatingSystem}, Domain: $domain, Timeout: ${timeout.inSeconds}s');

      // 只在Windows和Android平台执行更新检查，iOS平台跳过
      if (!Platform.isWindows && !Platform.isAndroid) {
        _logService.debug('UpdateManager', 'Skipping update check - platform not supported: ${Platform.operatingSystem}');
        return true;
      }

      // Android平台特殊调试信息
      if (Platform.isAndroid) {
        _logService.info('UpdateManager', 'Android platform detected - proceeding with update check');
      }

      // 检查全局冷却时间
      if (!_shouldCheckForUpdate()) {
        final timeSinceLastCheck = DateTime.now().difference(_lastCheckTime!);
        _logService.info('UpdateManager', 'Skipping update check due to cooldown. '
            'Last check: ${timeSinceLastCheck.inMinutes} minutes ago');
        return true;
      }

      // 创建临时更新服务实例（禁用自动检查）
      final config = UpdateConfig.defaultConfig().copyWith(
        enableAutoCheck: false,
        checkOnStartup: false,
      );
      final updateService = UpdateService(config: config);

      await updateService.initialize();

      try {
        // 使用带超时的检查方法
        final updateInfo = await updateService.checkForUpdateWithTimeout(
          domain: domain,
          timeout: timeout,
        );

        // 记录检查时间（无论是否有更新）
        _recordCheckTime();

        if (updateInfo.updateAvailable) {
          _logService.info('UpdateManager', 'Update available: ${updateInfo.version}');

          // 显示更新对话框
          if (context.mounted) {
            return await _showUpdateDialog(context, updateInfo);
          }
        } else {
          _logService.debug('UpdateManager', 'No update available');
        }

        return true; // 继续原操作
      } finally {
        await updateService.dispose();
      }
    } catch (e) {
      _logService.warning('UpdateManager', 'Pre-action update check failed: $e');
      return true; // 失败时继续原操作
    }
  }

  /// 手动检查更新（用于关于界面的手动检查）
  ///
  /// DESCRIPTION:
  ///     手动检查更新，强制检查忽略冷却时间
  ///
  /// PARAMETERS:
  ///     context - 构建上下文
  ///     domain - 用户域名（可选）
  ///
  /// RETURNS:
  ///     Future<void>
  static Future<void> checkForUpdateManually(
    BuildContext context, {
    String? domain,
  }) async {
    try {
      _logService.info('UpdateManager', 'Starting manual update check...');

      // 创建临时更新服务实例（禁用自动检查）
      final config = UpdateConfig.defaultConfig().copyWith(
        enableAutoCheck: false,
        checkOnStartup: false,
      );
      final updateService = UpdateService(config: config);

      await updateService.initialize();

      try {
        // 手动检查时强制检查，忽略60分钟限制
        final updateInfo = await updateService.checkForUpdate(
          domain: domain,
          forceCheck: true,
        );

        if (!context.mounted) return;

        final l10n = AppLocalizations.of(context)!;

        // 显示检查结果
        if (updateInfo.updateAvailable) {
          await _showUpdateDialog(context, updateInfo);
        } else {
          _showNoUpdateDialog(context, l10n);
        }
      } finally {
        await updateService.dispose();
      }
    } catch (e) {
      _logService.error('UpdateManager', 'Manual update check failed', e);

      if (context.mounted) {
        final l10n = AppLocalizations.of(context)!;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('${l10n.updateFailed}: $e')),
        );
      }
    }
  }



  /// 显示更新对话框
  static Future<bool> _showUpdateDialog(
    BuildContext context,
    UpdateInfo updateInfo,
  ) async {
    // 获取当前应用版本
    String currentVersion = "未知";
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      currentVersion = packageInfo.version;
    } catch (e) {
      _logService.warning('UpdateManager', 'Failed to get current version: $e');
    }

    // 检查context是否仍然有效
    if (!context.mounted) return true;

    final l10n = AppLocalizations.of(context)!;

    return await showDialog<bool>(
      context: context,
      barrierDismissible: !updateInfo.forceUpdate,
      builder: (context) => AlertDialog(
        title: Text(updateInfo.forceUpdate ? l10n.forceUpdate : l10n.updateAvailable),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${l10n.currentVersion}: $currentVersion'),
            Text('${l10n.latestVersion}: ${updateInfo.version}'),
            if (updateInfo.releaseNotes != null && updateInfo.releaseNotes!.isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(
                l10n.releaseNotes,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              Text(_getReleaseNotesForLocale(updateInfo, l10n.localeName)),
            ],
            if (updateInfo.forceUpdate) ...[
              const SizedBox(height: 12),
              Text(
                l10n.forceUpdateMessage,
                style: TextStyle(
                  color: Colors.red[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ],
        ),
        actions: [
          if (!updateInfo.forceUpdate)
            TextButton(
              onPressed: () => Navigator.of(context).pop(true), // 继续原操作
              child: Text(l10n.updateLater),
            ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop(false); // 停止原操作
              _handleUpdateNow(context, updateInfo); // 开始更新
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
            ),
            child: Text(l10n.updateNow),
          ),
        ],
      ),
    ) ?? true; // 如果对话框被意外关闭，默认继续原操作
  }

  /// 显示无更新对话框
  static void _showNoUpdateDialog(BuildContext context, AppLocalizations l10n) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.checkUpdate),
        content: Text(l10n.updateNotAvailable),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(l10n.ok),
          ),
        ],
      ),
    );
  }

  /// 根据语言环境获取发布说明
  static String _getReleaseNotesForLocale(UpdateInfo updateInfo, String localeName) {
    if (updateInfo.releaseNotes == null || updateInfo.releaseNotes!.isEmpty) {
      return '';
    }

    // 尝试获取当前语言的发布说明
    String languageCode = localeName.split('_')[0]; // 从 'en_US' 获取 'en'

    // 优先使用完整的locale（如 'en_US'）
    if (updateInfo.releaseNotes!.containsKey(localeName)) {
      return updateInfo.releaseNotes![localeName]!;
    }

    // 其次使用语言代码（如 'en'）
    if (updateInfo.releaseNotes!.containsKey(languageCode)) {
      return updateInfo.releaseNotes![languageCode]!;
    }

    // 如果没有找到对应语言，尝试中文
    if (updateInfo.releaseNotes!.containsKey('zh')) {
      return updateInfo.releaseNotes!['zh']!;
    }

    // 最后返回第一个可用的语言
    return updateInfo.releaseNotes!.values.first;
  }

  /// 处理立即更新
  static void _handleUpdateNow(BuildContext context, UpdateInfo updateInfo) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => UpdateProgressDialog(updateInfo: updateInfo),
    );
  }


}
