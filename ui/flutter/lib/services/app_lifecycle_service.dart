/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// FILE NAME :      app_lifecycle_service.dart
///
/// DESCRIPTION :    应用程序生命周期服务，负责管理后端服务的生命周期，
///                  包括启动、监控、健康检查和关闭后端服务
///
/// AUTHOR :         wei
///
/// HISTORY :        10/06/2025 create

import 'dart:async';
import 'dart:io';

import 'api_service.dart';
import 'log_service.dart';
import 'backend_service.dart';
import 'memory_monitor_service.dart';

/// AppLifecycleService
///
/// PURPOSE:
///     应用程序生命周期服务，负责管理后端服务的完整生命周期
///
/// FEATURES:
///     - 后端服务启动：自动启动或检测已运行的后端服务
///     - 健康检查：定期检查后端服务运行状态
///     - 状态监控：实时监控后端服务状态变化
///     - 服务停止：安全停止后端服务
///     - 资源管理：自动清理定时器和资源
///
/// USAGE:
///     在应用初始化时创建实例并调用init()方法，
///     在应用退出时调用dispose()方法清理资源
class AppLifecycleService {
  /// API服务实例，用于健康检查
  final ApiService apiService;

  /// 日志服务实例，用于记录生命周期事件
  final LogService logService;

  /// 健康检查定时器
  Timer? _healthCheckTimer;

  /// 后端服务运行状态标志
  bool _isBackendRunning = false;

  /// 健康检查间隔时间
  final Duration _healthCheckInterval = const Duration(seconds: 10);

  /// 内存监控服务实例（仅Windows平台）
  MemoryMonitorService? _memoryMonitorService;

  /// AppLifecycleService构造函数
  ///
  /// DESCRIPTION:
  ///     创建应用生命周期服务实例
  ///
  /// PARAMETERS:
  ///     apiService - API服务实例，用于健康检查
  ///     logService - 日志服务实例，用于记录事件
  AppLifecycleService({
    required this.apiService,
    required this.logService,
  });

  /// init
  ///
  /// DESCRIPTION:
  ///     初始化应用生命周期服务，启动健康检查定时器和内存监控（Windows平台）
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> init() async {
    _startHealthCheck();

    // 在Windows平台启动内存监控
    if (Platform.isWindows) {
      _memoryMonitorService = MemoryMonitorService(logService);
      _memoryMonitorService!.start();
      logService.info('AppLifecycle', 'Memory monitoring started for Windows platform');
    }

    logService.info('AppLifecycle', 'AppLifecycleService initialized');
  }

  /// startBackendService
  ///
  /// DESCRIPTION:
  ///     启动后端服务，如果服务已运行则直接返回成功
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<bool> - 启动成功返回true，失败返回false
  ///
  /// THROWS:
  ///     Exception - 启动过程中发生错误时记录日志并返回false
  Future<bool> startBackendService() async {
    if (_isBackendRunning) {
      logService.info('AppLifecycle', 'Backend service is already running');
      return true;
    }

    logService.info('AppLifecycle', 'Starting backend service...');

    try {
      // 检查后端服务是否已经在运行
      final isRunning = await _checkBackendRunning();
      if (isRunning) {
        _isBackendRunning = true;
        logService.info('AppLifecycle', 'Backend service is already running externally');
        return true;
      }

      // 使用BackendService启动后端服务
      final backendService = BackendService(logService: logService);
      final success = await backendService.start();

      if (success) {
        _isBackendRunning = true;
        logService.info('AppLifecycle', 'Backend service started successfully via BackendService');
        return true;
      }

      logService.error('AppLifecycle', 'Backend service failed to start via BackendService');
      return false;
    } catch (e) {
      logService.error('AppLifecycle', 'Failed to start backend service', e);
      return false;
    }
  }

  /// stopBackendService
  ///
  /// DESCRIPTION:
  ///     停止后端服务，如果服务未运行则直接返回
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  ///
  /// THROWS:
  ///     Exception - 停止过程中发生错误时记录日志但不抛出异常
  Future<void> stopBackendService() async {
    if (!_isBackendRunning) {
      return;
    }

    logService.info('AppLifecycle', 'Stopping backend service...');

    try {
      // 使用BackendService停止后端服务
      final backendService = BackendService(logService: logService);
      await backendService.stopBackend();

      _isBackendRunning = false;
      logService.info('AppLifecycle', 'Backend service stopped');
    } catch (e) {
      logService.error('AppLifecycle', 'Failed to stop backend service', e);
    }
  }

  /// _checkBackendRunning
  ///
  /// DESCRIPTION:
  ///     检查后端服务是否正在运行，通过健康检查API验证
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<bool> - 服务运行返回true，否则返回false
  Future<bool> _checkBackendRunning() async {
    try {
      final isHealthy = await apiService.healthCheck();
      return isHealthy;
    } catch (e) {
      return false;
    }
  }

  /// _startHealthCheck
  ///
  /// DESCRIPTION:
  ///     启动健康检查定时器，定期检查后端服务状态
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  void _startHealthCheck() {
    _stopHealthCheck();

    _healthCheckTimer = Timer.periodic(_healthCheckInterval, (_) async {
      final isRunning = await _checkBackendRunning();

      if (isRunning != _isBackendRunning) {
        _isBackendRunning = isRunning;
        if (isRunning) {
          logService.info('AppLifecycle', 'Backend service is now running');
        } else {
          logService.warning('AppLifecycle', 'Backend service is no longer running');
        }
      }
    });
  }

  /// _stopHealthCheck
  ///
  /// DESCRIPTION:
  ///     停止健康检查定时器
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  void _stopHealthCheck() {
    _healthCheckTimer?.cancel();
    _healthCheckTimer = null;
  }

  /// isBackendRunning
  ///
  /// DESCRIPTION:
  ///     获取后端服务当前运行状态
  ///
  /// RETURNS:
  ///     bool - 服务运行返回true，否则返回false
  bool get isBackendRunning => _isBackendRunning;

  /// dispose
  ///
  /// DESCRIPTION:
  ///     释放服务资源，停止健康检查定时器、内存监控和后端服务
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> dispose() async {
    _stopHealthCheck();

    // 停止内存监控
    if (_memoryMonitorService != null) {
      _memoryMonitorService!.dispose();
      _memoryMonitorService = null;
      logService.info('AppLifecycle', 'Memory monitoring stopped');
    }

    await stopBackendService();
  }
}
