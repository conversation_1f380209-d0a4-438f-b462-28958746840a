import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_zh.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
      : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('zh')
  ];

  /// No description provided for @appTitle.
  ///
  /// In en, this message translates to:
  /// **'Panabit iWAN'**
  String get appTitle;

  /// No description provided for @login.
  ///
  /// In en, this message translates to:
  /// **'Login'**
  String get login;

  /// No description provided for @username.
  ///
  /// In en, this message translates to:
  /// **'Username'**
  String get username;

  /// No description provided for @password.
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get password;

  /// No description provided for @domain.
  ///
  /// In en, this message translates to:
  /// **'Domain'**
  String get domain;

  /// No description provided for @rememberCredentials.
  ///
  /// In en, this message translates to:
  /// **'Remember credentials'**
  String get rememberCredentials;

  /// No description provided for @loginButton.
  ///
  /// In en, this message translates to:
  /// **'Login'**
  String get loginButton;

  /// No description provided for @loginFailed.
  ///
  /// In en, this message translates to:
  /// **'Login failed'**
  String get loginFailed;

  /// No description provided for @loginSuccess.
  ///
  /// In en, this message translates to:
  /// **'Login successful'**
  String get loginSuccess;

  /// No description provided for @logout.
  ///
  /// In en, this message translates to:
  /// **'Logout'**
  String get logout;

  /// No description provided for @connect.
  ///
  /// In en, this message translates to:
  /// **'Connect'**
  String get connect;

  /// No description provided for @disconnect.
  ///
  /// In en, this message translates to:
  /// **'Disconnect'**
  String get disconnect;

  /// No description provided for @connecting.
  ///
  /// In en, this message translates to:
  /// **'Connecting...'**
  String get connecting;

  /// No description provided for @connected.
  ///
  /// In en, this message translates to:
  /// **'Connected'**
  String get connected;

  /// No description provided for @disconnected.
  ///
  /// In en, this message translates to:
  /// **'Not Connected'**
  String get disconnected;

  /// No description provided for @disconnecting.
  ///
  /// In en, this message translates to:
  /// **'Disconnecting...'**
  String get disconnecting;

  /// No description provided for @reconnecting.
  ///
  /// In en, this message translates to:
  /// **'Reconnecting...'**
  String get reconnecting;

  /// No description provided for @connectionFailed.
  ///
  /// In en, this message translates to:
  /// **'Connection failed'**
  String get connectionFailed;

  /// No description provided for @connectionSuccess.
  ///
  /// In en, this message translates to:
  /// **'Connection successful'**
  String get connectionSuccess;

  /// No description provided for @serverList.
  ///
  /// In en, this message translates to:
  /// **'Server List'**
  String get serverList;

  /// No description provided for @selectServer.
  ///
  /// In en, this message translates to:
  /// **'Select Server'**
  String get selectServer;

  /// No description provided for @noServersAvailable.
  ///
  /// In en, this message translates to:
  /// **'No servers available'**
  String get noServersAvailable;

  /// No description provided for @refreshServers.
  ///
  /// In en, this message translates to:
  /// **'Refresh Servers'**
  String get refreshServers;

  /// No description provided for @settings.
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// No description provided for @about.
  ///
  /// In en, this message translates to:
  /// **'About'**
  String get about;

  /// No description provided for @statistics.
  ///
  /// In en, this message translates to:
  /// **'Statistics'**
  String get statistics;

  /// No description provided for @logs.
  ///
  /// In en, this message translates to:
  /// **'Logs'**
  String get logs;

  /// No description provided for @language.
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// No description provided for @theme.
  ///
  /// In en, this message translates to:
  /// **'Theme'**
  String get theme;

  /// No description provided for @autoConnect.
  ///
  /// In en, this message translates to:
  /// **'Auto Connect'**
  String get autoConnect;

  /// No description provided for @minimizeToTray.
  ///
  /// In en, this message translates to:
  /// **'Minimize to Tray'**
  String get minimizeToTray;

  /// No description provided for @startWithSystem.
  ///
  /// In en, this message translates to:
  /// **'Start with System'**
  String get startWithSystem;

  /// No description provided for @connectionTime.
  ///
  /// In en, this message translates to:
  /// **'Connection Time'**
  String get connectionTime;

  /// No description provided for @dataTransferred.
  ///
  /// In en, this message translates to:
  /// **'Data Transferred'**
  String get dataTransferred;

  /// No description provided for @ping.
  ///
  /// In en, this message translates to:
  /// **'Ping'**
  String get ping;

  /// No description provided for @latency.
  ///
  /// In en, this message translates to:
  /// **'Latency'**
  String get latency;

  /// No description provided for @version.
  ///
  /// In en, this message translates to:
  /// **'Version'**
  String get version;

  /// No description provided for @copyright.
  ///
  /// In en, this message translates to:
  /// **'Copyright'**
  String get copyright;

  /// No description provided for @termsOfService.
  ///
  /// In en, this message translates to:
  /// **'Terms of Service'**
  String get termsOfService;

  /// No description provided for @privacyPolicy.
  ///
  /// In en, this message translates to:
  /// **'Privacy Policy'**
  String get privacyPolicy;

  /// No description provided for @acceptLicense.
  ///
  /// In en, this message translates to:
  /// **'I accept the Terms of Service and Privacy Policy'**
  String get acceptLicense;

  /// No description provided for @licenseRequired.
  ///
  /// In en, this message translates to:
  /// **'You must accept the license agreement to continue'**
  String get licenseRequired;

  /// No description provided for @error.
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get error;

  /// No description provided for @warning.
  ///
  /// In en, this message translates to:
  /// **'Warning'**
  String get warning;

  /// No description provided for @info.
  ///
  /// In en, this message translates to:
  /// **'Information'**
  String get info;

  /// No description provided for @success.
  ///
  /// In en, this message translates to:
  /// **'Success'**
  String get success;

  /// No description provided for @cancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// No description provided for @ok.
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get ok;

  /// No description provided for @yes.
  ///
  /// In en, this message translates to:
  /// **'Yes'**
  String get yes;

  /// No description provided for @no.
  ///
  /// In en, this message translates to:
  /// **'No'**
  String get no;

  /// No description provided for @save.
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get save;

  /// No description provided for @close.
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get close;

  /// No description provided for @exit.
  ///
  /// In en, this message translates to:
  /// **'Exit'**
  String get exit;

  /// No description provided for @minimize.
  ///
  /// In en, this message translates to:
  /// **'Minimize'**
  String get minimize;

  /// No description provided for @maximize.
  ///
  /// In en, this message translates to:
  /// **'Maximize'**
  String get maximize;

  /// No description provided for @restore.
  ///
  /// In en, this message translates to:
  /// **'Restore'**
  String get restore;

  /// No description provided for @copy.
  ///
  /// In en, this message translates to:
  /// **'Copy'**
  String get copy;

  /// No description provided for @paste.
  ///
  /// In en, this message translates to:
  /// **'Paste'**
  String get paste;

  /// No description provided for @cut.
  ///
  /// In en, this message translates to:
  /// **'Cut'**
  String get cut;

  /// No description provided for @selectAll.
  ///
  /// In en, this message translates to:
  /// **'Select All'**
  String get selectAll;

  /// No description provided for @clear.
  ///
  /// In en, this message translates to:
  /// **'Clear'**
  String get clear;

  /// No description provided for @refresh.
  ///
  /// In en, this message translates to:
  /// **'Refresh'**
  String get refresh;

  /// No description provided for @retry.
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get retry;

  /// No description provided for @loading.
  ///
  /// In en, this message translates to:
  /// **'Loading...'**
  String get loading;

  /// No description provided for @pleaseWait.
  ///
  /// In en, this message translates to:
  /// **'Please wait...'**
  String get pleaseWait;

  /// No description provided for @networkError.
  ///
  /// In en, this message translates to:
  /// **'Network error'**
  String get networkError;

  /// No description provided for @serverError.
  ///
  /// In en, this message translates to:
  /// **'Server error'**
  String get serverError;

  /// No description provided for @connectionTimeout.
  ///
  /// In en, this message translates to:
  /// **'Connection timeout'**
  String get connectionTimeout;

  /// No description provided for @invalidCredentials.
  ///
  /// In en, this message translates to:
  /// **'Invalid credentials'**
  String get invalidCredentials;

  /// No description provided for @sessionExpired.
  ///
  /// In en, this message translates to:
  /// **'Session expired'**
  String get sessionExpired;

  /// No description provided for @accessDenied.
  ///
  /// In en, this message translates to:
  /// **'Access denied'**
  String get accessDenied;

  /// No description provided for @serviceUnavailable.
  ///
  /// In en, this message translates to:
  /// **'Service unavailable'**
  String get serviceUnavailable;

  /// No description provided for @mainScreen.
  ///
  /// In en, this message translates to:
  /// **'Main'**
  String get mainScreen;

  /// No description provided for @connectionScreen.
  ///
  /// In en, this message translates to:
  /// **'Connection'**
  String get connectionScreen;

  /// No description provided for @userScreen.
  ///
  /// In en, this message translates to:
  /// **'User'**
  String get userScreen;

  /// No description provided for @settingsScreen.
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settingsScreen;

  /// No description provided for @aboutScreen.
  ///
  /// In en, this message translates to:
  /// **'About'**
  String get aboutScreen;

  /// No description provided for @logsScreen.
  ///
  /// In en, this message translates to:
  /// **'Logs'**
  String get logsScreen;

  /// No description provided for @statisticsScreen.
  ///
  /// In en, this message translates to:
  /// **'Statistics'**
  String get statisticsScreen;

  /// No description provided for @enterServerDomain.
  ///
  /// In en, this message translates to:
  /// **'Enter Server Domain'**
  String get enterServerDomain;

  /// No description provided for @serverDomain.
  ///
  /// In en, this message translates to:
  /// **'Server Domain'**
  String get serverDomain;

  /// No description provided for @serverDomainHint.
  ///
  /// In en, this message translates to:
  /// **'e.g.: vpn.example.com'**
  String get serverDomainHint;

  /// No description provided for @nextStep.
  ///
  /// In en, this message translates to:
  /// **'Next'**
  String get nextStep;

  /// No description provided for @clientDomain.
  ///
  /// In en, this message translates to:
  /// **'Client Domain'**
  String get clientDomain;

  /// No description provided for @change.
  ///
  /// In en, this message translates to:
  /// **'Change'**
  String get change;

  /// No description provided for @rememberUsernamePassword.
  ///
  /// In en, this message translates to:
  /// **'Remember username and password'**
  String get rememberUsernamePassword;

  /// No description provided for @pleaseEnterServerDomain.
  ///
  /// In en, this message translates to:
  /// **'Please enter server domain'**
  String get pleaseEnterServerDomain;

  /// No description provided for @pleaseEnterUsername.
  ///
  /// In en, this message translates to:
  /// **'Please enter username'**
  String get pleaseEnterUsername;

  /// No description provided for @pleaseEnterPassword.
  ///
  /// In en, this message translates to:
  /// **'Please enter password'**
  String get pleaseEnterPassword;

  /// No description provided for @pleaseEnterClientDomain.
  ///
  /// In en, this message translates to:
  /// **'Please enter client domain'**
  String get pleaseEnterClientDomain;

  /// No description provided for @clientDomainHint.
  ///
  /// In en, this message translates to:
  /// **'e.g.: research.staff.unisase'**
  String get clientDomainHint;

  /// No description provided for @lookupServiceError.
  ///
  /// In en, this message translates to:
  /// **'Failed to query server address'**
  String get lookupServiceError;

  /// No description provided for @lookupServiceTimeout.
  ///
  /// In en, this message translates to:
  /// **'Server address query timeout'**
  String get lookupServiceTimeout;

  /// No description provided for @lookupServiceInvalidResponse.
  ///
  /// In en, this message translates to:
  /// **'Invalid response from server'**
  String get lookupServiceInvalidResponse;

  /// No description provided for @lookupServiceNetworkError.
  ///
  /// In en, this message translates to:
  /// **'Network connection failed, please check network settings'**
  String get lookupServiceNetworkError;

  /// No description provided for @queryingServerAddress.
  ///
  /// In en, this message translates to:
  /// **'Querying server address...'**
  String get queryingServerAddress;

  /// No description provided for @startingBackendService.
  ///
  /// In en, this message translates to:
  /// **'Starting backend service...'**
  String get startingBackendService;

  /// No description provided for @backendServiceStartFailed.
  ///
  /// In en, this message translates to:
  /// **'Failed to start backend service, please check permissions or restart application'**
  String get backendServiceStartFailed;

  /// No description provided for @checkingBackendServiceStatus.
  ///
  /// In en, this message translates to:
  /// **'Starting...'**
  String get checkingBackendServiceStatus;

  /// No description provided for @backendServiceHealthCheckFailed.
  ///
  /// In en, this message translates to:
  /// **'Backend service health check failed, please check if port is occupied or restart application'**
  String get backendServiceHealthCheckFailed;

  /// No description provided for @autoStart.
  ///
  /// In en, this message translates to:
  /// **'Auto Start'**
  String get autoStart;

  /// No description provided for @autoStartEnabled.
  ///
  /// In en, this message translates to:
  /// **'Auto start enabled'**
  String get autoStartEnabled;

  /// No description provided for @autoStartDisabled.
  ///
  /// In en, this message translates to:
  /// **'Auto start disabled'**
  String get autoStartDisabled;

  /// No description provided for @routingSettings.
  ///
  /// In en, this message translates to:
  /// **'Routing Settings'**
  String get routingSettings;

  /// No description provided for @applying.
  ///
  /// In en, this message translates to:
  /// **'Applying...'**
  String get applying;

  /// No description provided for @applySettings.
  ///
  /// In en, this message translates to:
  /// **'Apply Settings'**
  String get applySettings;

  /// No description provided for @settingsAppliedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Settings applied successfully'**
  String get settingsAppliedSuccessfully;

  /// No description provided for @noChangesToApply.
  ///
  /// In en, this message translates to:
  /// **'No changes to apply'**
  String get noChangesToApply;

  /// No description provided for @getRoutingSettingsFailed.
  ///
  /// In en, this message translates to:
  /// **'Failed to get routing settings'**
  String get getRoutingSettingsFailed;

  /// No description provided for @saveAutoStartSettingFailed.
  ///
  /// In en, this message translates to:
  /// **'Failed to save auto start setting'**
  String get saveAutoStartSettingFailed;

  /// No description provided for @saveRoutingSettingsFailed.
  ///
  /// In en, this message translates to:
  /// **'Failed to save routing settings'**
  String get saveRoutingSettingsFailed;

  /// No description provided for @appSettings.
  ///
  /// In en, this message translates to:
  /// **'App Settings'**
  String get appSettings;

  /// No description provided for @appName.
  ///
  /// In en, this message translates to:
  /// **'Application Name'**
  String get appName;

  /// No description provided for @versionNumber.
  ///
  /// In en, this message translates to:
  /// **'Version'**
  String get versionNumber;

  /// No description provided for @deviceId.
  ///
  /// In en, this message translates to:
  /// **'Device ID'**
  String get deviceId;

  /// No description provided for @gettingDeviceId.
  ///
  /// In en, this message translates to:
  /// **'Getting...'**
  String get gettingDeviceId;

  /// No description provided for @getDeviceIdFailed.
  ///
  /// In en, this message translates to:
  /// **'Failed to get'**
  String get getDeviceIdFailed;

  /// No description provided for @agreementsAndContact.
  ///
  /// In en, this message translates to:
  /// **'Agreements'**
  String get agreementsAndContact;

  /// No description provided for @viewTermsOfService.
  ///
  /// In en, this message translates to:
  /// **'View Terms of Service'**
  String get viewTermsOfService;

  /// No description provided for @viewPrivacyPolicy.
  ///
  /// In en, this message translates to:
  /// **'View Privacy Policy'**
  String get viewPrivacyPolicy;

  /// No description provided for @officialWebsite.
  ///
  /// In en, this message translates to:
  /// **'Official Website'**
  String get officialWebsite;

  /// No description provided for @technicalSupport.
  ///
  /// In en, this message translates to:
  /// **'Technical Support'**
  String get technicalSupport;

  /// No description provided for @openLinkFailed.
  ///
  /// In en, this message translates to:
  /// **'Failed to open link'**
  String get openLinkFailed;

  /// No description provided for @clickToView.
  ///
  /// In en, this message translates to:
  /// **'Click to view'**
  String get clickToView;

  /// No description provided for @sendEmailFailed.
  ///
  /// In en, this message translates to:
  /// **'Failed to send email'**
  String get sendEmailFailed;

  /// No description provided for @vpnClientFeedback.
  ///
  /// In en, this message translates to:
  /// **'WAN Client Feedback'**
  String get vpnClientFeedback;

  /// No description provided for @allUrlLaunchMethodsFailed.
  ///
  /// In en, this message translates to:
  /// **'All URL launch methods failed'**
  String get allUrlLaunchMethodsFailed;

  /// No description provided for @openLinkFailedWithError.
  ///
  /// In en, this message translates to:
  /// **'Failed to open link'**
  String get openLinkFailedWithError;

  /// No description provided for @sendEmailFailedWithError.
  ///
  /// In en, this message translates to:
  /// **'Failed to send email'**
  String get sendEmailFailedWithError;

  /// No description provided for @statisticsInfo.
  ///
  /// In en, this message translates to:
  /// **'Statistics'**
  String get statisticsInfo;

  /// No description provided for @status.
  ///
  /// In en, this message translates to:
  /// **'Status'**
  String get status;

  /// No description provided for @interface.
  ///
  /// In en, this message translates to:
  /// **'Interface'**
  String get interface;

  /// No description provided for @upload.
  ///
  /// In en, this message translates to:
  /// **'Upload'**
  String get upload;

  /// No description provided for @download.
  ///
  /// In en, this message translates to:
  /// **'Download'**
  String get download;

  /// No description provided for @localIp.
  ///
  /// In en, this message translates to:
  /// **'Local IP'**
  String get localIp;

  /// No description provided for @itforceIp.
  ///
  /// In en, this message translates to:
  /// **'Cloud IP'**
  String get itforceIp;

  /// No description provided for @personalInfo.
  ///
  /// In en, this message translates to:
  /// **'Personal Information'**
  String get personalInfo;

  /// No description provided for @editPersonalInfo.
  ///
  /// In en, this message translates to:
  /// **'Edit Personal Information'**
  String get editPersonalInfo;

  /// No description provided for @name.
  ///
  /// In en, this message translates to:
  /// **'Name'**
  String get name;

  /// No description provided for @pleaseEnterName.
  ///
  /// In en, this message translates to:
  /// **'Please enter name'**
  String get pleaseEnterName;

  /// No description provided for @department.
  ///
  /// In en, this message translates to:
  /// **'Department'**
  String get department;

  /// No description provided for @position.
  ///
  /// In en, this message translates to:
  /// **'Position'**
  String get position;

  /// No description provided for @accountInfo.
  ///
  /// In en, this message translates to:
  /// **'Account Information'**
  String get accountInfo;

  /// No description provided for @clientDomainLabel.
  ///
  /// In en, this message translates to:
  /// **'Client Domain'**
  String get clientDomainLabel;

  /// No description provided for @usernameLabel.
  ///
  /// In en, this message translates to:
  /// **'Username'**
  String get usernameLabel;

  /// No description provided for @deviceInfo.
  ///
  /// In en, this message translates to:
  /// **'Device ID'**
  String get deviceInfo;

  /// No description provided for @logoutButton.
  ///
  /// In en, this message translates to:
  /// **'Logout'**
  String get logoutButton;

  /// No description provided for @personalInfoSaved.
  ///
  /// In en, this message translates to:
  /// **'Personal information saved'**
  String get personalInfoSaved;

  /// No description provided for @saveFailed.
  ///
  /// In en, this message translates to:
  /// **'Save failed, please try again'**
  String get saveFailed;

  /// No description provided for @notSet.
  ///
  /// In en, this message translates to:
  /// **'Not set'**
  String get notSet;

  /// No description provided for @editUserInfo.
  ///
  /// In en, this message translates to:
  /// **'Edit User Info'**
  String get editUserInfo;

  /// No description provided for @confirmLogout.
  ///
  /// In en, this message translates to:
  /// **'Confirm Logout'**
  String get confirmLogout;

  /// No description provided for @logoutConfirmation.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to logout?'**
  String get logoutConfirmation;

  /// No description provided for @logoutWithVpnWarning.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to logout?\n\nNote: Current WAN connection will be automatically disconnected.'**
  String get logoutWithVpnWarning;

  /// No description provided for @disconnectAndExit.
  ///
  /// In en, this message translates to:
  /// **'Disconnect and Exit'**
  String get disconnectAndExit;

  /// No description provided for @clearLogs.
  ///
  /// In en, this message translates to:
  /// **'Clear Logs'**
  String get clearLogs;

  /// No description provided for @confirmClearLogs.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to clear all logs?'**
  String get confirmClearLogs;

  /// No description provided for @confirm.
  ///
  /// In en, this message translates to:
  /// **'Confirm'**
  String get confirm;

  /// No description provided for @confirmReconnection.
  ///
  /// In en, this message translates to:
  /// **'Confirm Reconnection'**
  String get confirmReconnection;

  /// No description provided for @routingChangeRequiresReconnection.
  ///
  /// In en, this message translates to:
  /// **'Current VPN is connected. Changing routing settings requires reconnection. Do you want to confirm and reconnect?'**
  String get routingChangeRequiresReconnection;

  /// No description provided for @confirmAndReconnect.
  ///
  /// In en, this message translates to:
  /// **'Confirm and Reconnect'**
  String get confirmAndReconnect;

  /// No description provided for @routingSettingsAppliedAndReconnected.
  ///
  /// In en, this message translates to:
  /// **'Routing settings saved and reconnected'**
  String get routingSettingsAppliedAndReconnected;

  /// No description provided for @routingSettingsAppliedDisconnected.
  ///
  /// In en, this message translates to:
  /// **'Routing settings saved, VPN disconnected'**
  String get routingSettingsAppliedDisconnected;

  /// No description provided for @routingSettingsReconnectionFailed.
  ///
  /// In en, this message translates to:
  /// **'Routing settings reconnection failed'**
  String get routingSettingsReconnectionFailed;

  /// No description provided for @logsExportedTo.
  ///
  /// In en, this message translates to:
  /// **'Logs exported to'**
  String get logsExportedTo;

  /// No description provided for @exportLogsFailed.
  ///
  /// In en, this message translates to:
  /// **'Failed to export logs'**
  String get exportLogsFailed;

  /// No description provided for @logCopiedToClipboard.
  ///
  /// In en, this message translates to:
  /// **'Log copied to clipboard'**
  String get logCopiedToClipboard;

  /// No description provided for @allLevels.
  ///
  /// In en, this message translates to:
  /// **'All Levels'**
  String get allLevels;

  /// No description provided for @searchLogs.
  ///
  /// In en, this message translates to:
  /// **'Search logs...'**
  String get searchLogs;

  /// No description provided for @logsTitle.
  ///
  /// In en, this message translates to:
  /// **'Logs'**
  String get logsTitle;

  /// No description provided for @closeSearch.
  ///
  /// In en, this message translates to:
  /// **'Close Search'**
  String get closeSearch;

  /// No description provided for @searchLogsTooltip.
  ///
  /// In en, this message translates to:
  /// **'Search Logs'**
  String get searchLogsTooltip;

  /// No description provided for @filterByLevel.
  ///
  /// In en, this message translates to:
  /// **'Filter by Level'**
  String get filterByLevel;

  /// No description provided for @moreActions.
  ///
  /// In en, this message translates to:
  /// **'More Actions'**
  String get moreActions;

  /// No description provided for @exportLogs.
  ///
  /// In en, this message translates to:
  /// **'Export Logs'**
  String get exportLogs;

  /// No description provided for @filterPrefix.
  ///
  /// In en, this message translates to:
  /// **'Filter: '**
  String get filterPrefix;

  /// No description provided for @clearFilter.
  ///
  /// In en, this message translates to:
  /// **'Clear Filter'**
  String get clearFilter;

  /// No description provided for @noLogs.
  ///
  /// In en, this message translates to:
  /// **'No logs'**
  String get noLogs;

  /// No description provided for @noData.
  ///
  /// In en, this message translates to:
  /// **'No data'**
  String get noData;

  /// No description provided for @scrollToLatestLog.
  ///
  /// In en, this message translates to:
  /// **'Scroll to Latest Log'**
  String get scrollToLatestLog;

  /// No description provided for @connectionManagement.
  ///
  /// In en, this message translates to:
  /// **'Connection Management'**
  String get connectionManagement;

  /// No description provided for @userInfo.
  ///
  /// In en, this message translates to:
  /// **'User Information'**
  String get userInfo;

  /// No description provided for @trafficStatistics.
  ///
  /// In en, this message translates to:
  /// **'Traffic Statistics'**
  String get trafficStatistics;

  /// No description provided for @systemLogs.
  ///
  /// In en, this message translates to:
  /// **'System Logs'**
  String get systemLogs;

  /// No description provided for @aboutApp.
  ///
  /// In en, this message translates to:
  /// **'About App'**
  String get aboutApp;

  /// No description provided for @connection.
  ///
  /// In en, this message translates to:
  /// **'Connection'**
  String get connection;

  /// No description provided for @user.
  ///
  /// In en, this message translates to:
  /// **'User'**
  String get user;

  /// No description provided for @statisticsNav.
  ///
  /// In en, this message translates to:
  /// **'Statistics'**
  String get statisticsNav;

  /// No description provided for @settingsNav.
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settingsNav;

  /// No description provided for @logsNav.
  ///
  /// In en, this message translates to:
  /// **'Logs'**
  String get logsNav;

  /// No description provided for @aboutNav.
  ///
  /// In en, this message translates to:
  /// **'About'**
  String get aboutNav;

  /// No description provided for @auto.
  ///
  /// In en, this message translates to:
  /// **'Auto'**
  String get auto;

  /// No description provided for @statusUpdated.
  ///
  /// In en, this message translates to:
  /// **'Status updated'**
  String get statusUpdated;

  /// No description provided for @routingMode.
  ///
  /// In en, this message translates to:
  /// **'Routing Mode'**
  String get routingMode;

  /// No description provided for @allRouting.
  ///
  /// In en, this message translates to:
  /// **'All Routing'**
  String get allRouting;

  /// No description provided for @allRoutingDescription.
  ///
  /// In en, this message translates to:
  /// **'All traffic goes through WAN tunnel'**
  String get allRoutingDescription;

  /// No description provided for @customRouting.
  ///
  /// In en, this message translates to:
  /// **'Custom Routing'**
  String get customRouting;

  /// No description provided for @customRoutingDescription.
  ///
  /// In en, this message translates to:
  /// **'Only specified network segments go through WAN tunnel'**
  String get customRoutingDescription;

  /// No description provided for @enterNetworkSegments.
  ///
  /// In en, this message translates to:
  /// **'Please enter network segments to route'**
  String get enterNetworkSegments;

  /// No description provided for @networkSegmentsExample.
  ///
  /// In en, this message translates to:
  /// **'Separate multiple segments with commas, e.g.: ***********/16,10.0.0.0/8'**
  String get networkSegmentsExample;

  /// No description provided for @enterNetworkSegmentsHint.
  ///
  /// In en, this message translates to:
  /// **'Enter network segments...'**
  String get enterNetworkSegmentsHint;

  /// No description provided for @ensureCorrectCidrFormat.
  ///
  /// In en, this message translates to:
  /// **'Please ensure correct CIDR format'**
  String get ensureCorrectCidrFormat;

  /// No description provided for @uploadSpeed.
  ///
  /// In en, this message translates to:
  /// **'Upload'**
  String get uploadSpeed;

  /// No description provided for @downloadSpeed.
  ///
  /// In en, this message translates to:
  /// **'Download'**
  String get downloadSpeed;

  /// No description provided for @unreachable.
  ///
  /// In en, this message translates to:
  /// **'Unreachable'**
  String get unreachable;

  /// No description provided for @excellent.
  ///
  /// In en, this message translates to:
  /// **'Excellent'**
  String get excellent;

  /// No description provided for @good.
  ///
  /// In en, this message translates to:
  /// **'Good'**
  String get good;

  /// No description provided for @poor.
  ///
  /// In en, this message translates to:
  /// **'Poor'**
  String get poor;

  /// No description provided for @languageSettings.
  ///
  /// In en, this message translates to:
  /// **'Language Settings'**
  String get languageSettings;

  /// No description provided for @selectLanguage.
  ///
  /// In en, this message translates to:
  /// **'Select Language'**
  String get selectLanguage;

  /// No description provided for @chinese.
  ///
  /// In en, this message translates to:
  /// **'中文'**
  String get chinese;

  /// No description provided for @english.
  ///
  /// In en, this message translates to:
  /// **'English'**
  String get english;

  /// No description provided for @languageChanged.
  ///
  /// In en, this message translates to:
  /// **'Language changed'**
  String get languageChanged;

  /// No description provided for @pleaseSelectServer.
  ///
  /// In en, this message translates to:
  /// **'Please select a server first'**
  String get pleaseSelectServer;

  /// No description provided for @connectingToServer.
  ///
  /// In en, this message translates to:
  /// **'Connecting...'**
  String get connectingToServer;

  /// No description provided for @disconnectingFromServer.
  ///
  /// In en, this message translates to:
  /// **'Disconnecting...'**
  String get disconnectingFromServer;

  /// No description provided for @connectionTimeoutDetailed.
  ///
  /// In en, this message translates to:
  /// **'Connection timeout, please check network connection or try again later'**
  String get connectionTimeoutDetailed;

  /// No description provided for @connectionFailedGeneric.
  ///
  /// In en, this message translates to:
  /// **'Connection failed'**
  String get connectionFailedGeneric;

  /// No description provided for @disconnectedFromServer.
  ///
  /// In en, this message translates to:
  /// **'Disconnected'**
  String get disconnectedFromServer;

  /// No description provided for @switchingToServer.
  ///
  /// In en, this message translates to:
  /// **'Switching to {serverName}...'**
  String switchingToServer(Object serverName);

  /// No description provided for @connectedToServer.
  ///
  /// In en, this message translates to:
  /// **'Connected to {serverName}'**
  String connectedToServer(Object serverName);

  /// No description provided for @currentlyConnectedTo.
  ///
  /// In en, this message translates to:
  /// **'Currently connected to {serverName}, switching to {newServerName}'**
  String currentlyConnectedTo(Object newServerName, Object serverName);

  /// No description provided for @selectServerFirst.
  ///
  /// In en, this message translates to:
  /// **'Please select a server first'**
  String get selectServerFirst;

  /// No description provided for @operationTimeout.
  ///
  /// In en, this message translates to:
  /// **'Operation timeout, please try again later'**
  String get operationTimeout;

  /// No description provided for @pingServersFailed.
  ///
  /// In en, this message translates to:
  /// **'Ping servers failed: {error}'**
  String pingServersFailed(Object error);

  /// No description provided for @networkConnectionFailed.
  ///
  /// In en, this message translates to:
  /// **'Network connection failed, please check network settings'**
  String get networkConnectionFailed;

  /// No description provided for @realtimeConnectionInterrupted.
  ///
  /// In en, this message translates to:
  /// **'Real-time connection interrupted, attempting to reconnect...'**
  String get realtimeConnectionInterrupted;

  /// No description provided for @authenticationFailed.
  ///
  /// In en, this message translates to:
  /// **'Authentication failed, please login again'**
  String get authenticationFailed;

  /// No description provided for @operationFailedRetry.
  ///
  /// In en, this message translates to:
  /// **'Operation failed, please try again later'**
  String get operationFailedRetry;

  /// No description provided for @systemTrayConnected.
  ///
  /// In en, this message translates to:
  /// **'Connected'**
  String get systemTrayConnected;

  /// No description provided for @systemTrayConnecting.
  ///
  /// In en, this message translates to:
  /// **'Connecting...'**
  String get systemTrayConnecting;

  /// No description provided for @systemTrayDisconnecting.
  ///
  /// In en, this message translates to:
  /// **'Disconnecting...'**
  String get systemTrayDisconnecting;

  /// No description provided for @systemTrayDisconnected.
  ///
  /// In en, this message translates to:
  /// **'Disconnected'**
  String get systemTrayDisconnected;

  /// No description provided for @showWindow.
  ///
  /// In en, this message translates to:
  /// **'Show Window'**
  String get showWindow;

  /// No description provided for @hideWindow.
  ///
  /// In en, this message translates to:
  /// **'Hide Window'**
  String get hideWindow;

  /// No description provided for @exitApp.
  ///
  /// In en, this message translates to:
  /// **'Exit App'**
  String get exitApp;

  /// No description provided for @processing.
  ///
  /// In en, this message translates to:
  /// **'Processing...'**
  String get processing;

  /// No description provided for @calculating.
  ///
  /// In en, this message translates to:
  /// **'Calculating...'**
  String get calculating;

  /// No description provided for @operationCancelled.
  ///
  /// In en, this message translates to:
  /// **'Operation cancelled'**
  String get operationCancelled;

  /// No description provided for @testLatency.
  ///
  /// In en, this message translates to:
  /// **'Test Latency'**
  String get testLatency;

  /// No description provided for @testingLatency.
  ///
  /// In en, this message translates to:
  /// **'Testing latency...'**
  String get testingLatency;

  /// No description provided for @latencyTestComplete.
  ///
  /// In en, this message translates to:
  /// **'Latency test complete'**
  String get latencyTestComplete;

  /// No description provided for @currentlyConnectedToServer.
  ///
  /// In en, this message translates to:
  /// **'Currently connected to: {serverName}'**
  String currentlyConnectedToServer(Object serverName);

  /// No description provided for @unknownServer.
  ///
  /// In en, this message translates to:
  /// **'Unknown server'**
  String get unknownServer;

  /// No description provided for @noAutoServersAvailable.
  ///
  /// In en, this message translates to:
  /// **'No auto servers available, please check network connection or contact administrator'**
  String get noAutoServersAvailable;

  /// No description provided for @autoServerSelectionFailed.
  ///
  /// In en, this message translates to:
  /// **'Auto server selection failed, please select server manually or try again later'**
  String get autoServerSelectionFailed;

  /// No description provided for @apiInvalidRequest.
  ///
  /// In en, this message translates to:
  /// **'Invalid request format or parameters'**
  String get apiInvalidRequest;

  /// No description provided for @apiInvalidCredentials.
  ///
  /// In en, this message translates to:
  /// **'Invalid username or password'**
  String get apiInvalidCredentials;

  /// No description provided for @apiServerError.
  ///
  /// In en, this message translates to:
  /// **'Server internal error'**
  String get apiServerError;

  /// No description provided for @apiResourceNotFound.
  ///
  /// In en, this message translates to:
  /// **'Requested resource not found'**
  String get apiResourceNotFound;

  /// No description provided for @apiUnauthorized.
  ///
  /// In en, this message translates to:
  /// **'Unauthorized access, please login again'**
  String get apiUnauthorized;

  /// No description provided for @apiForbidden.
  ///
  /// In en, this message translates to:
  /// **'Access to this resource is forbidden'**
  String get apiForbidden;

  /// No description provided for @apiTimeout.
  ///
  /// In en, this message translates to:
  /// **'Request timeout, please try again later'**
  String get apiTimeout;

  /// No description provided for @apiConflict.
  ///
  /// In en, this message translates to:
  /// **'Resource conflict'**
  String get apiConflict;

  /// No description provided for @apiRateLimit.
  ///
  /// In en, this message translates to:
  /// **'Too many requests, please try again later'**
  String get apiRateLimit;

  /// No description provided for @apiGatewayError.
  ///
  /// In en, this message translates to:
  /// **'Gateway error'**
  String get apiGatewayError;

  /// No description provided for @apiServiceUnavailable.
  ///
  /// In en, this message translates to:
  /// **'Service temporarily unavailable, please try again later'**
  String get apiServiceUnavailable;

  /// No description provided for @networkUnreachable.
  ///
  /// In en, this message translates to:
  /// **'Network unreachable, please check network connection'**
  String get networkUnreachable;

  /// No description provided for @networkDnsFailure.
  ///
  /// In en, this message translates to:
  /// **'DNS resolution failed, please check server address'**
  String get networkDnsFailure;

  /// No description provided for @networkConnectionReset.
  ///
  /// In en, this message translates to:
  /// **'Network connection was reset'**
  String get networkConnectionReset;

  /// No description provided for @networkConnectionClosed.
  ///
  /// In en, this message translates to:
  /// **'Network connection was closed'**
  String get networkConnectionClosed;

  /// No description provided for @networkProxyError.
  ///
  /// In en, this message translates to:
  /// **'Proxy server error'**
  String get networkProxyError;

  /// No description provided for @networkTlsError.
  ///
  /// In en, this message translates to:
  /// **'TLS/SSL error'**
  String get networkTlsError;

  /// No description provided for @authInvalidCredentials.
  ///
  /// In en, this message translates to:
  /// **'Invalid user credentials'**
  String get authInvalidCredentials;

  /// No description provided for @authExpiredCredentials.
  ///
  /// In en, this message translates to:
  /// **'Credentials expired, please login again'**
  String get authExpiredCredentials;

  /// No description provided for @authRateLimit.
  ///
  /// In en, this message translates to:
  /// **'Authentication requests too frequent, please try again later'**
  String get authRateLimit;

  /// No description provided for @authAccountLocked.
  ///
  /// In en, this message translates to:
  /// **'Account locked, please contact administrator'**
  String get authAccountLocked;

  /// No description provided for @authInvalidToken.
  ///
  /// In en, this message translates to:
  /// **'Invalid authentication token'**
  String get authInvalidToken;

  /// No description provided for @authTokenExpired.
  ///
  /// In en, this message translates to:
  /// **'Authentication token expired, please login again'**
  String get authTokenExpired;

  /// No description provided for @authMissingCredentials.
  ///
  /// In en, this message translates to:
  /// **'Missing authentication credentials'**
  String get authMissingCredentials;

  /// No description provided for @tunnelError.
  ///
  /// In en, this message translates to:
  /// **'Tunnel error'**
  String get tunnelError;

  /// No description provided for @tunnelInitFailed.
  ///
  /// In en, this message translates to:
  /// **'Tunnel initialization failed'**
  String get tunnelInitFailed;

  /// No description provided for @tunnelCloseFailed.
  ///
  /// In en, this message translates to:
  /// **'Tunnel close failed'**
  String get tunnelCloseFailed;

  /// No description provided for @tunnelReadFailed.
  ///
  /// In en, this message translates to:
  /// **'Tunnel read failed'**
  String get tunnelReadFailed;

  /// No description provided for @tunnelWriteFailed.
  ///
  /// In en, this message translates to:
  /// **'Tunnel write failed'**
  String get tunnelWriteFailed;

  /// No description provided for @tunnelConfigFailed.
  ///
  /// In en, this message translates to:
  /// **'Tunnel configuration failed'**
  String get tunnelConfigFailed;

  /// No description provided for @configError.
  ///
  /// In en, this message translates to:
  /// **'Configuration error'**
  String get configError;

  /// No description provided for @configInvalid.
  ///
  /// In en, this message translates to:
  /// **'Invalid configuration'**
  String get configInvalid;

  /// No description provided for @configFileNotFound.
  ///
  /// In en, this message translates to:
  /// **'Configuration file not found'**
  String get configFileNotFound;

  /// No description provided for @configFileReadFailed.
  ///
  /// In en, this message translates to:
  /// **'Configuration file read failed'**
  String get configFileReadFailed;

  /// No description provided for @configFileWriteFailed.
  ///
  /// In en, this message translates to:
  /// **'Configuration file write failed'**
  String get configFileWriteFailed;

  /// No description provided for @configFileParseFailed.
  ///
  /// In en, this message translates to:
  /// **'Configuration file parse failed'**
  String get configFileParseFailed;

  /// No description provided for @platformError.
  ///
  /// In en, this message translates to:
  /// **'Platform error'**
  String get platformError;

  /// No description provided for @platformUnsupported.
  ///
  /// In en, this message translates to:
  /// **'Unsupported platform'**
  String get platformUnsupported;

  /// No description provided for @platformInitFailed.
  ///
  /// In en, this message translates to:
  /// **'Platform initialization failed'**
  String get platformInitFailed;

  /// No description provided for @platformIoError.
  ///
  /// In en, this message translates to:
  /// **'Platform IO error'**
  String get platformIoError;

  /// No description provided for @platformPermissionDenied.
  ///
  /// In en, this message translates to:
  /// **'Permission denied, please run as administrator'**
  String get platformPermissionDenied;

  /// No description provided for @domainLookupFailed.
  ///
  /// In en, this message translates to:
  /// **'Domain lookup failed'**
  String get domainLookupFailed;

  /// No description provided for @domainNotFound.
  ///
  /// In en, this message translates to:
  /// **'Domain not found, please check the domain name'**
  String get domainNotFound;

  /// No description provided for @domainInvalid.
  ///
  /// In en, this message translates to:
  /// **'Invalid domain format'**
  String get domainInvalid;

  /// No description provided for @domainRequired.
  ///
  /// In en, this message translates to:
  /// **'Domain parameter is required'**
  String get domainRequired;

  /// No description provided for @domainLookupTimeout.
  ///
  /// In en, this message translates to:
  /// **'Domain lookup timeout, please try again later'**
  String get domainLookupTimeout;

  /// No description provided for @domainLookupNetworkError.
  ///
  /// In en, this message translates to:
  /// **'Network error during domain lookup, please check your connection'**
  String get domainLookupNetworkError;

  /// No description provided for @serverListNotFound.
  ///
  /// In en, this message translates to:
  /// **'Server list not found, please check the server URL'**
  String get serverListNotFound;

  /// No description provided for @serverListInvalid.
  ///
  /// In en, this message translates to:
  /// **'Invalid server list format'**
  String get serverListInvalid;

  /// No description provided for @serverListTimeout.
  ///
  /// In en, this message translates to:
  /// **'Server list request timeout, please try again later'**
  String get serverListTimeout;

  /// No description provided for @serverListNetworkError.
  ///
  /// In en, this message translates to:
  /// **'Network error while fetching server list, please check your connection'**
  String get serverListNetworkError;

  /// No description provided for @protocolError.
  ///
  /// In en, this message translates to:
  /// **'Protocol error'**
  String get protocolError;

  /// No description provided for @protocolInvalid.
  ///
  /// In en, this message translates to:
  /// **'Invalid protocol'**
  String get protocolInvalid;

  /// No description provided for @protocolUnsupported.
  ///
  /// In en, this message translates to:
  /// **'Unsupported protocol'**
  String get protocolUnsupported;

  /// No description provided for @protocolVersionMismatch.
  ///
  /// In en, this message translates to:
  /// **'Protocol version mismatch'**
  String get protocolVersionMismatch;

  /// No description provided for @protocolHandshakeFailed.
  ///
  /// In en, this message translates to:
  /// **'Protocol handshake failed'**
  String get protocolHandshakeFailed;

  /// No description provided for @protocolEncryptionFailed.
  ///
  /// In en, this message translates to:
  /// **'Protocol encryption failed'**
  String get protocolEncryptionFailed;

  /// No description provided for @protocolDecryptionFailed.
  ///
  /// In en, this message translates to:
  /// **'Protocol decryption failed'**
  String get protocolDecryptionFailed;

  /// No description provided for @unknownError.
  ///
  /// In en, this message translates to:
  /// **'Unknown error'**
  String get unknownError;

  /// No description provided for @invalidParameter.
  ///
  /// In en, this message translates to:
  /// **'Invalid parameter'**
  String get invalidParameter;

  /// No description provided for @notImplemented.
  ///
  /// In en, this message translates to:
  /// **'Feature not implemented'**
  String get notImplemented;

  /// No description provided for @permissionDenied.
  ///
  /// In en, this message translates to:
  /// **'Permission denied'**
  String get permissionDenied;

  /// No description provided for @checkUpdate.
  ///
  /// In en, this message translates to:
  /// **'Check Update'**
  String get checkUpdate;

  /// No description provided for @checkingUpdate.
  ///
  /// In en, this message translates to:
  /// **'Checking for updates...'**
  String get checkingUpdate;

  /// No description provided for @updateAvailable.
  ///
  /// In en, this message translates to:
  /// **'Update Available'**
  String get updateAvailable;

  /// No description provided for @updateNotAvailable.
  ///
  /// In en, this message translates to:
  /// **'No updates available'**
  String get updateNotAvailable;

  /// No description provided for @currentVersion.
  ///
  /// In en, this message translates to:
  /// **'Current Version'**
  String get currentVersion;

  /// No description provided for @latestVersion.
  ///
  /// In en, this message translates to:
  /// **'Latest Version'**
  String get latestVersion;

  /// No description provided for @updateNow.
  ///
  /// In en, this message translates to:
  /// **'Update Now'**
  String get updateNow;

  /// No description provided for @updateLater.
  ///
  /// In en, this message translates to:
  /// **'Remind Later'**
  String get updateLater;

  /// No description provided for @skipUpdate.
  ///
  /// In en, this message translates to:
  /// **'Skip Update'**
  String get skipUpdate;

  /// No description provided for @downloading.
  ///
  /// In en, this message translates to:
  /// **'Downloading...'**
  String get downloading;

  /// No description provided for @downloadProgress.
  ///
  /// In en, this message translates to:
  /// **'Download Progress'**
  String get downloadProgress;

  /// No description provided for @downloadComplete.
  ///
  /// In en, this message translates to:
  /// **'Download Complete'**
  String get downloadComplete;

  /// No description provided for @downloadFailed.
  ///
  /// In en, this message translates to:
  /// **'Download Failed'**
  String get downloadFailed;

  /// No description provided for @installing.
  ///
  /// In en, this message translates to:
  /// **'Installing...'**
  String get installing;

  /// No description provided for @installComplete.
  ///
  /// In en, this message translates to:
  /// **'Installation Complete'**
  String get installComplete;

  /// No description provided for @installFailed.
  ///
  /// In en, this message translates to:
  /// **'Installation Failed'**
  String get installFailed;

  /// No description provided for @installStarted.
  ///
  /// In en, this message translates to:
  /// **'Installation started. Please tap \'Install\' in the system dialog to complete the app update. Please restart the app manually after installation.'**
  String get installStarted;

  /// No description provided for @restartAppMessage.
  ///
  /// In en, this message translates to:
  /// **'Update installation complete. Please restart the app to use the new version.'**
  String get restartAppMessage;

  /// No description provided for @updateFailed.
  ///
  /// In en, this message translates to:
  /// **'Update Failed'**
  String get updateFailed;

  /// No description provided for @updateCancelled.
  ///
  /// In en, this message translates to:
  /// **'Update Cancelled'**
  String get updateCancelled;

  /// No description provided for @forceUpdate.
  ///
  /// In en, this message translates to:
  /// **'Force Update'**
  String get forceUpdate;

  /// No description provided for @forceUpdateMessage.
  ///
  /// In en, this message translates to:
  /// **'This update contains critical security fixes and must be installed immediately'**
  String get forceUpdateMessage;

  /// No description provided for @releaseNotes.
  ///
  /// In en, this message translates to:
  /// **'Release Notes'**
  String get releaseNotes;

  /// No description provided for @fileValidationFailed.
  ///
  /// In en, this message translates to:
  /// **'File validation failed'**
  String get fileValidationFailed;

  /// No description provided for @insufficientStorage.
  ///
  /// In en, this message translates to:
  /// **'Insufficient storage space'**
  String get insufficientStorage;

  /// No description provided for @networkUnavailable.
  ///
  /// In en, this message translates to:
  /// **'Network connection unavailable'**
  String get networkUnavailable;

  /// No description provided for @wifiRequired.
  ///
  /// In en, this message translates to:
  /// **'WiFi connection required'**
  String get wifiRequired;

  /// No description provided for @permissionRequired.
  ///
  /// In en, this message translates to:
  /// **'Installation permission required'**
  String get permissionRequired;

  /// No description provided for @versionInfo.
  ///
  /// In en, this message translates to:
  /// **'Version Info'**
  String get versionInfo;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['en', 'zh'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en':
      return AppLocalizationsEn();
    case 'zh':
      return AppLocalizationsZh();
  }

  throw FlutterError(
      'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
      'an issue with the localizations generation tool. Please file an issue '
      'on GitHub with a reproducible sample app and the gen-l10n configuration '
      'that was used.');
}
