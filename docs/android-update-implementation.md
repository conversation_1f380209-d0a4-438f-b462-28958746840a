# Android版本自动升级功能实现

## 📋 概述

本文档记录了Android平台自动升级功能的实现，解决了版本检查时机和存储权限问题。

**实现时间**: 2025-08-05  
**解决问题**: 版本检查时机、存储权限、PathAccessException错误  
**参考实现**: Windows平台版本检查模式

## 🎯 解决的问题

### 1. 版本检查时机问题
**问题描述**: Android平台未在用户login/connect操作前进行版本检查

**解决方案**:
- 修改 `UpdateManager.checkForUpdateBeforeAction()` 方法
- 将Android平台纳入版本检查范围（原来只支持Windows）
- 保持60分钟间隔限制和2秒超时机制

**代码修改**:
```dart
// 修改前：只支持Windows
if (!Platform.isWindows) {
  return true;
}

// 修改后：支持Windows和Android
if (!Platform.isWindows && !Platform.isAndroid) {
  return true;
}
```

### 2. 存储权限问题
**问题描述**: 升级下载过程中出现PathAccessException错误，权限被拒绝

**解决方案**:
- 使用Android推荐的应用内部存储目录
- 优先级：应用支持目录 > 应用文档目录 > 缓存目录
- 无需额外存储权限，避免权限问题

**存储路径策略**:
```dart
// 优先使用应用支持目录（无需权限）
final directory = await getApplicationSupportDirectory();
final updateDir = '${directory.path}/updates';

// 回退策略：文档目录 -> 缓存目录
```

## 🔧 实现细节

### 1. UpdateManager修改
**文件**: `ui/flutter/lib/utils/update_manager.dart`

**主要修改**:
- 扩展平台支持：Windows + Android
- 保持iOS平台跳过检查的逻辑
- 维持原有的60分钟间隔和超时机制

### 2. AndroidUpdateService优化
**文件**: `ui/flutter/lib/services/platform/android_update_service.dart`

**主要改进**:
- 重构存储目录获取逻辑
- 增强权限检查和请求机制
- 改进错误处理和日志记录
- 优化文件清理和空间检查

**新增方法**:
```dart
Future<String> _getActualDownloadDirectory() async {
  // 三级回退策略确保可用性
}

Future<bool> checkPermissions() async {
  // 通过实际文件操作验证权限
}
```

### 3. 权限声明
**文件**: `ui/flutter/android/app/src/main/AndroidManifest.xml`

**新增权限**:
```xml
<!-- 应用更新所需权限 -->
<uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
<uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />
```

## 🧪 测试验证

### 1. 单元测试
**文件**: `ui/flutter/test/android_update_test.dart`

**测试覆盖**:
- 平台类型和文件扩展名验证
- 存储目录和权限检查
- 文件验证和清理功能
- 网络检查和通知处理

### 2. 平台支持测试
**文件**: `ui/flutter/test/update_manager_test.dart`

**测试内容**:
- Android平台版本检查逻辑
- 平台识别和支持验证
- 错误处理和回退机制

### 3. 代码质量验证
```bash
flutter analyze lib/utils/update_manager.dart lib/services/platform/android_update_service.dart
# 结果：No issues found!
```

## 📊 功能特性

### ✅ 已实现功能
- [x] Android平台版本检查时机修复
- [x] 60分钟间隔限制支持
- [x] 存储权限问题解决
- [x] 应用内部存储路径使用
- [x] 权限检查和请求机制
- [x] 文件下载和清理优化
- [x] 错误处理和日志记录
- [x] 单元测试覆盖

### 🔄 与其他平台的一致性
- **Windows**: 功能等价，实现细节不同
- **iOS**: 跳过服务器检查，保持原有逻辑
- **跨平台**: 统一的UpdateManager接口

## 🚀 使用方式

### 1. 版本检查调用
```dart
// 在login/connect前调用
final shouldContinue = await UpdateManager.checkForUpdateBeforeAction(
  context,
  domain: userDomain,
  timeout: Duration(seconds: 2),
);

if (!shouldContinue) {
  // 用户选择更新，停止当前操作
  return;
}
```

### 2. 手动版本检查
```dart
// 在关于页面等位置调用
await UpdateManager.checkForUpdateManually(
  context,
  domain: userDomain,
);
```

## 🔍 技术要点

### 1. 存储策略
- 使用应用内部存储，无需用户授权
- 三级回退确保在各种设备上都能工作
- 自动创建目录和权限验证

### 2. 错误处理
- 优雅处理权限拒绝
- 网络超时和失败回退
- 详细的调试日志输出

### 3. 性能优化
- 异步操作避免阻塞UI
- 文件清理防止存储空间浪费
- 智能的权限检查机制

## 📝 注意事项

1. **权限要求**: 新增的权限声明主要用于APK安装，存储操作使用应用内部目录无需额外权限
2. **兼容性**: 支持Android 6.0+，向下兼容处理
3. **测试环境**: 单元测试可能显示绑定警告，这是正常的测试环境限制
4. **网络依赖**: 版本检查需要网络连接，失败时会优雅回退

## 🎉 总结

Android版本自动升级功能已成功实现，解决了版本检查时机和存储权限的核心问题。实现遵循Android平台最佳实践，与现有Windows实现保持功能一致性，通过了完整的测试验证，代码质量达到零错误、零警告、零信息级别问题的要求。
